{"pagination": {"ListApiKeys": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ListDataSources": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "dataSources"}, "ListFunctions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "functions"}, "ListGraphqlApis": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "graphqlApis"}, "ListResolvers": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "resolvers"}, "ListResolversByFunction": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "resolvers"}, "ListTypes": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "types"}}}