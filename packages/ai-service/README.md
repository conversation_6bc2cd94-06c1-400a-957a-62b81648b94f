# AI Service for Decentralized Marketplace

This service provides AI-powered product analysis for the decentralized marketplace, including tag suggestion, spam detection, and recommendation scoring.

## Features

- **Tag Suggestion**: Automatically suggests relevant tags for product categorization
- **Spam/Scam Detection**: Identifies potentially fraudulent listings with reasoning
- **Product Scoring**: Generates recommendation scores based on product quality
- **Debug Mode**: Provides detailed AI reasoning for transparency

## Architecture

- **AWS Bedrock Integration**: Uses Claude/Titan models for analysis
- **RESTful API**: Flask-based service with JSON endpoints
- **Chainlink Functions Ready**: Designed for future blockchain integration

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run locally
python app.py

# Test with sample data
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -d @data/sample_products.json
```

## API Endpoints

### POST /analyze
Analyzes product listings and returns insights.

**Request Body:**
```json
[
  {
    "productId": 1,
    "title": "iPhone 13 Pro Max",
    "description": "Brand new, sealed box. Comes with warranty.",
    "seller": "0xabc123...",
    "tags": ["electronics", "mobile"],
    "timestamp": **********
  }
]
```

**Response:**
```json
{
  "actions": [
    {
      "productId": 1,
      "recommendedTags": ["electronics", "smartphone", "apple"],
      "score": 0.95,
      "flagged": false
    }
  ]
}
```

### GET /health
Health check endpoint.

## Configuration

Set environment variables:
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_REGION`: AWS region (default: us-east-1)
- `BEDROCK_MODEL_ID`: Model ID (default: anthropic.claude-3-sonnet-20240229-v1:0)

## Development

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Format code
black .
flake8 .
```
