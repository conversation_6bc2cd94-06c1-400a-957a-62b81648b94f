#!/usr/bin/env python3
"""
Test script for the AI Service API
Run this to test the service locally
"""

import json
import requests
import time

# Configuration
BASE_URL = "http://localhost:5001"

def load_sample_data():
    """Load sample product data"""
    try:
        with open('data/mock_products.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Sample data file not found. Using minimal test data.")
        return [
            {
                "productId": 1,
                "title": "iPhone 13 Pro Max",
                "description": "Brand new, sealed box. Comes with warranty.",
                "seller": "0xabc123...",
                "tags": ["electronics", "mobile"],
                "timestamp": **********
            },
            {
                "productId": 2,
                "title": "FREE CRYPTO GIVEAWAY!!! CLICK NOW!!!",
                "description": "🚨🚨🚨 URGENT!!! Get FREE Bitcoin and Ethereum NOW!!!",
                "seller": "0xscam123...",
                "tags": [],
                "timestamp": **********
            }
        ]

def test_health_check():
    """Test health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the service is running.")
        return False

def test_analyze_products():
    """Test product analysis endpoint"""
    print("\nTesting product analysis...")
    
    sample_data = load_sample_data()
    # Use first 5 products for testing
    test_products = sample_data[:5]
    
    try:
        response = requests.post(
            f"{BASE_URL}/analyze",
            json=test_products,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Analyzed {len(result['actions'])} products")
            
            for action in result['actions']:
                product_id = action['productId']
                score = action['score']
                flagged = action['flagged']
                tags = action['recommendedTags']
                
                print(f"\nProduct {product_id}:")
                print(f"  Score: {score}")
                print(f"  Flagged: {flagged}")
                print(f"  Tags: {', '.join(tags)}")
                
                if flagged and 'reason' in action:
                    print(f"  Reason: {action['reason']}")
        else:
            print(f"Error: {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_debug_mode():
    """Test analysis with debug mode"""
    print("\nTesting debug mode...")
    
    sample_product = {
        "productId": 999,
        "title": "Test Product with Organic Materials",
        "description": "This is a test product made with sustainable organic materials.",
        "seller": "0xtest123...",
        "tags": ["test"],
        "timestamp": int(time.time())
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/analyze/single?debug=true",
            json=sample_product,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("Debug analysis result:")
            print(json.dumps(result, indent=2))
        else:
            print(f"Error: {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_spam_detection():
    """Test spam detection specifically"""
    print("\nTesting spam detection...")
    
    spam_products = [
        {
            "productId": 1001,
            "title": "URGENT!!! FREE MONEY!!!",
            "description": "Click now to get FREE Bitcoin!!! Limited time offer!!!",
            "seller": "0xspammer...",
            "tags": [],
            "timestamp": int(time.time())
        },
        {
            "productId": 1002,
            "title": "Quality Handmade Ceramic Mug",
            "description": "Beautiful handcrafted ceramic mug made by local artisan.",
            "seller": "0xlegitimate...",
            "tags": ["handmade", "ceramic"],
            "timestamp": int(time.time())
        }
    ]
    
    try:
        response = requests.post(
            f"{BASE_URL}/analyze",
            json=spam_products,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            
            for action in result['actions']:
                product_id = action['productId']
                flagged = action['flagged']
                score = action['score']
                
                print(f"\nProduct {product_id}:")
                print(f"  Flagged as spam: {flagged}")
                print(f"  Quality score: {score}")
                
                if flagged:
                    print(f"  ✅ Correctly identified spam")
                else:
                    print(f"  ✅ Correctly identified as legitimate")
        
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing AI Service API")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Product Analysis", test_analyze_products),
        ("Debug Mode", test_debug_mode),
        ("Spam Detection", test_spam_detection)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name} passed")
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The AI service is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the service configuration.")

if __name__ == "__main__":
    main()
