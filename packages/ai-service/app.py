#!/usr/bin/env python3
"""
AI Service for Decentralized Marketplace
Provides product analysis including tag suggestion, spam detection, and scoring.
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
AWS_REGION = os.getenv('AWS_REGION', 'us-east-1')
BEDROCK_MODEL_ID = os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
USE_MOCK_RESPONSES = os.getenv('USE_MOCK_RESPONSES', 'true').lower() == 'true'

class AIAnalyzer:
    """AI-powered product analyzer using AWS Bedrock"""
    
    def __init__(self):
        self.bedrock_client = None
        if not USE_MOCK_RESPONSES:
            try:
                self.bedrock_client = boto3.client(
                    service_name='bedrock-runtime',
                    region_name=AWS_REGION
                )
                logger.info("AWS Bedrock client initialized successfully")
            except (NoCredentialsError, Exception) as e:
                logger.warning(f"Failed to initialize Bedrock client: {e}")
                logger.info("Falling back to mock responses")
                self.bedrock_client = None
    
    def analyze_products(self, products: List[Dict], debug: bool = False) -> Dict[str, Any]:
        """Analyze a list of products and return insights"""
        actions = []
        
        for product in products:
            try:
                analysis = self._analyze_single_product(product, debug)
                actions.append(analysis)
            except Exception as e:
                logger.error(f"Error analyzing product {product.get('productId', 'unknown')}: {e}")
                # Return safe fallback
                actions.append({
                    "productId": product.get('productId'),
                    "recommendedTags": product.get('tags', []),
                    "score": 0.5,
                    "flagged": False,
                    "error": "Analysis failed"
                })
        
        result = {"actions": actions}
        if debug:
            result["debug"] = {
                "timestamp": datetime.utcnow().isoformat(),
                "model_used": "mock" if USE_MOCK_RESPONSES else BEDROCK_MODEL_ID,
                "total_products": len(products)
            }
        
        return result
    
    def _analyze_single_product(self, product: Dict, debug: bool = False) -> Dict[str, Any]:
        """Analyze a single product"""
        if self.bedrock_client and not USE_MOCK_RESPONSES:
            return self._analyze_with_bedrock(product, debug)
        else:
            return self._analyze_with_mock(product, debug)
    
    def _analyze_with_bedrock(self, product: Dict, debug: bool = False) -> Dict[str, Any]:
        """Analyze product using AWS Bedrock"""
        prompt = self._create_analysis_prompt(product)
        
        try:
            response = self.bedrock_client.invoke_model(
                modelId=BEDROCK_MODEL_ID,
                body=json.dumps({
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 1000,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                })
            )
            
            response_body = json.loads(response['body'].read())
            ai_response = response_body['content'][0]['text']
            
            # Parse AI response (expecting JSON format)
            analysis = json.loads(ai_response)
            
            if debug:
                analysis["debug_info"] = {
                    "prompt_used": prompt,
                    "raw_response": ai_response,
                    "model": BEDROCK_MODEL_ID
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Bedrock analysis failed: {e}")
            return self._analyze_with_mock(product, debug)
    
    def _analyze_with_mock(self, product: Dict, debug: bool = False) -> Dict[str, Any]:
        """Analyze product using mock AI responses"""
        title = product.get('title', '').lower()
        description = product.get('description', '').lower()
        existing_tags = product.get('tags', [])
        
        # Mock spam detection
        spam_indicators = [
            'free', 'urgent', '!!!', 'click now', 'limited time', 'guaranteed',
            'make money', 'work from home', '$$$', 'double your', 'giveaway'
        ]
        
        is_spam = any(indicator in title or indicator in description 
                     for indicator in spam_indicators)
        
        # Mock tag suggestion
        tag_mapping = {
            'iphone': ['electronics', 'smartphone', 'apple', 'mobile'],
            'macbook': ['electronics', 'laptop', 'apple', 'computer'],
            'tesla': ['automotive', 'electric', 'car', 'vehicle'],
            'coffee': ['food', 'beverage', 'artisan'],
            'yoga': ['fitness', 'wellness', 'exercise'],
            'gaming': ['electronics', 'computer', 'entertainment'],
            'organic': ['sustainable', 'eco-friendly', 'natural'],
            'vintage': ['collectible', 'retro', 'antique'],
            'solar': ['renewable', 'energy', 'sustainable'],
            'handmade': ['artisan', 'craft', 'unique']
        }
        
        suggested_tags = set(existing_tags)
        for keyword, tags in tag_mapping.items():
            if keyword in title or keyword in description:
                suggested_tags.update(tags)
        
        # Mock scoring
        score = 0.9 if not is_spam else 0.2
        if 'organic' in title or 'sustainable' in title:
            score += 0.05
        if len(description) > 50:
            score += 0.05
        
        score = min(score, 1.0)
        
        analysis = {
            "productId": product.get('productId'),
            "recommendedTags": list(suggested_tags)[:10],  # Limit to 10 tags
            "score": round(score, 2),
            "flagged": is_spam
        }
        
        if is_spam:
            analysis["reason"] = "Detected spam keywords and suspicious patterns"
        
        if debug:
            analysis["debug_info"] = {
                "spam_indicators_found": [ind for ind in spam_indicators 
                                        if ind in title or ind in description],
                "keywords_matched": [kw for kw in tag_mapping.keys() 
                                   if kw in title or kw in description],
                "original_tags": existing_tags,
                "analysis_method": "mock_ai"
            }
        
        return analysis
    
    def _create_analysis_prompt(self, product: Dict) -> str:
        """Create analysis prompt for Bedrock"""
        return f"""
Analyze this marketplace product listing and provide insights in JSON format:

Product Details:
- Title: {product.get('title', '')}
- Description: {product.get('description', '')}
- Current Tags: {product.get('tags', [])}
- Seller: {product.get('seller', '')}

Please analyze and return a JSON response with:
1. "productId": {product.get('productId')}
2. "recommendedTags": array of 5-10 relevant tags for categorization
3. "score": quality score from 0.0 to 1.0 (higher = better quality/trustworthiness)
4. "flagged": boolean indicating if this appears to be spam/scam
5. "reason": (only if flagged=true) explanation of why it's flagged

Consider:
- Product authenticity and legitimacy
- Description quality and completeness
- Pricing reasonableness
- Seller trustworthiness indicators
- Spam/scam patterns (excessive caps, urgency, too-good-to-be-true offers)

Return only valid JSON, no additional text.
"""

# Initialize analyzer
analyzer = AIAnalyzer()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "mock_mode": USE_MOCK_RESPONSES
    })

@app.route('/analyze', methods=['POST'])
def analyze_products():
    """Analyze product listings endpoint"""
    try:
        # Validate request
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        products = request.get_json()
        if not isinstance(products, list):
            return jsonify({"error": "Request body must be an array of products"}), 400
        
        # Get debug parameter
        debug = request.args.get('debug', 'false').lower() == 'true'
        
        # Analyze products
        result = analyzer.analyze_products(products, debug)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Analysis endpoint error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/analyze/single', methods=['POST'])
def analyze_single_product():
    """Analyze a single product endpoint"""
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        product = request.get_json()
        debug = request.args.get('debug', 'false').lower() == 'true'
        
        result = analyzer.analyze_products([product], debug)
        
        # Return just the single analysis
        if result["actions"]:
            single_result = result["actions"][0]
            if debug and "debug" in result:
                single_result["debug"] = result["debug"]
            return jsonify(single_result)
        else:
            return jsonify({"error": "Analysis failed"}), 500
            
    except Exception as e:
        logger.error(f"Single analysis endpoint error: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug_mode = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting AI Service on port {port}")
    logger.info(f"Mock mode: {USE_MOCK_RESPONSES}")
    logger.info(f"Debug mode: {debug_mode}")
    
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
