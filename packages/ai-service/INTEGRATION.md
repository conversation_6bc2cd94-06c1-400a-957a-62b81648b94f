# Chainlink Functions Integration Guide

This guide explains how to integrate the AI Service with Chainlink Functions for on-chain access to AI-powered product analysis.

## Overview

The AI Service provides product analysis capabilities that can be accessed from smart contracts via Chainlink Functions. This enables on-chain applications to leverage AI insights for:

- Product quality scoring
- Spam/scam detection
- Tag suggestions for categorization
- Trust scoring for marketplace listings

## Architecture

```
Smart Contract → Chainlink Functions → AI Service → AWS Bedrock (optional)
                                    ↓
                              Product Analysis Results
```

## Chainlink Functions Implementation

### 1. Functions Source Code

Create a Chainlink Functions source file (`ai-analysis.js`):

```javascript
// Chainlink Functions source code for AI product analysis
const productData = args[0]; // Product data from smart contract
const debug = args[1] || false; // Optional debug flag

// AI Service endpoint (replace with your deployed URL)
const apiUrl = "https://your-api-gateway-url.amazonaws.com/stage/analyze/single";

// Make HTTP request to AI service
const aiRequest = Functions.makeHttpRequest({
  url: apiUrl,
  method: "POST",
  headers: {
    "Content-Type": "application/json"
  },
  data: JSON.stringify(productData),
  params: debug ? { debug: "true" } : {}
});

// Handle the response
const response = await aiRequest;

if (response.error) {
  throw Error(`AI Service request failed: ${response.error}`);
}

const analysis = response.data;

// Validate response structure
if (!analysis.score || analysis.flagged === undefined) {
  throw Error("Invalid AI service response format");
}

// Return encoded results for on-chain use
// Score as uint256 (0-100), flagged as boolean
const encodedScore = Math.floor(analysis.score * 100);
const encodedFlagged = analysis.flagged ? 1 : 0;

// Return multiple values: [score, flagged, tagCount]
return Functions.encodeUint256(
  (encodedScore << 16) | (encodedFlagged << 8) | analysis.recommendedTags.length
);
```

### 2. Smart Contract Integration

Example smart contract that uses the AI analysis:

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {FunctionsClient} from "@chainlink/contracts/src/v0.8/functions/dev/v1_0_0/FunctionsClient.sol";
import {FunctionsRequest} from "@chainlink/contracts/src/v0.8/functions/dev/v1_0_0/libraries/FunctionsRequest.sol";

contract AIMarketplace is FunctionsClient {
    using FunctionsRequest for FunctionsRequest.Request;

    struct Product {
        uint256 id;
        string title;
        string description;
        address seller;
        string[] tags;
        uint256 timestamp;
        uint8 aiScore;      // 0-100
        bool flagged;       // AI flagged as spam
        bool analyzed;      // Has been analyzed
    }

    mapping(uint256 => Product) public products;
    mapping(bytes32 => uint256) public requestToProductId;
    
    bytes32 public donId;
    string public aiAnalysisSource;
    uint64 public subscriptionId;
    uint32 public gasLimit = 300000;

    event ProductAnalyzed(uint256 indexed productId, uint8 score, bool flagged);
    event AnalysisRequested(uint256 indexed productId, bytes32 requestId);

    constructor(
        address router,
        bytes32 _donId,
        uint64 _subscriptionId,
        string memory _aiAnalysisSource
    ) FunctionsClient(router) {
        donId = _donId;
        subscriptionId = _subscriptionId;
        aiAnalysisSource = _aiAnalysisSource;
    }

    function addProduct(
        uint256 _id,
        string memory _title,
        string memory _description,
        string[] memory _tags
    ) external {
        products[_id] = Product({
            id: _id,
            title: _title,
            description: _description,
            seller: msg.sender,
            tags: _tags,
            timestamp: block.timestamp,
            aiScore: 0,
            flagged: false,
            analyzed: false
        });

        // Automatically request AI analysis
        requestAIAnalysis(_id);
    }

    function requestAIAnalysis(uint256 _productId) public {
        Product storage product = products[_productId];
        require(product.id != 0, "Product does not exist");

        // Prepare product data for AI analysis
        string memory productJson = string(abi.encodePacked(
            '{"productId":', Strings.toString(_productId),
            ',"title":"', product.title,
            '","description":"', product.description,
            '","seller":"', Strings.toHexString(uint160(product.seller), 20),
            '","timestamp":', Strings.toString(product.timestamp), '}'
        ));

        // Create Chainlink Functions request
        FunctionsRequest.Request memory req;
        req.initializeRequestForInlineJavaScript(aiAnalysisSource);
        req.setArgs([productJson, "false"]); // productData, debug

        // Send request
        bytes32 requestId = _sendRequest(
            req.encodeCBOR(),
            subscriptionId,
            gasLimit,
            donId
        );

        requestToProductId[requestId] = _productId;
        emit AnalysisRequested(_productId, requestId);
    }

    function fulfillRequest(
        bytes32 requestId,
        bytes memory response,
        bytes memory err
    ) internal override {
        if (err.length > 0) {
            // Handle error
            return;
        }

        uint256 productId = requestToProductId[requestId];
        require(productId != 0, "Invalid request ID");

        // Decode the response
        uint256 encodedResult = abi.decode(response, (uint256));
        
        uint8 score = uint8((encodedResult >> 16) & 0xFF);
        bool flagged = ((encodedResult >> 8) & 0xFF) == 1;
        uint8 tagCount = uint8(encodedResult & 0xFF);

        // Update product with AI analysis
        Product storage product = products[productId];
        product.aiScore = score;
        product.flagged = flagged;
        product.analyzed = true;

        emit ProductAnalyzed(productId, score, flagged);
        delete requestToProductId[requestId];
    }

    function getProductAnalysis(uint256 _productId) 
        external 
        view 
        returns (uint8 score, bool flagged, bool analyzed) 
    {
        Product storage product = products[_productId];
        return (product.aiScore, product.flagged, product.analyzed);
    }

    function isProductTrusted(uint256 _productId) external view returns (bool) {
        Product storage product = products[_productId];
        return product.analyzed && !product.flagged && product.aiScore >= 70;
    }
}
```

### 3. Deployment Configuration

#### Environment Variables

```bash
# Chainlink Functions Configuration
CHAINLINK_ROUTER_ADDRESS=0x... # Functions router address
DON_ID=0x... # DON ID for your network
SUBSCRIPTION_ID=123 # Your Functions subscription ID
FUNCTIONS_SOURCE_PATH=./ai-analysis.js

# AI Service Configuration
AI_SERVICE_URL=https://your-api-gateway-url.amazonaws.com/stage
```

#### Deployment Script

```javascript
// deploy/01_deploy_ai_marketplace.js
const { network } = require("hardhat");
const fs = require("fs");

module.exports = async ({ getNamedAccounts, deployments }) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();

  // Read the Functions source code
  const aiAnalysisSource = fs.readFileSync("./ai-analysis.js", "utf8");

  // Network-specific configuration
  const config = {
    sepolia: {
      router: "0xb83E47C2bC239B3bf370bc41e1459A34b41238D0",
      donId: "0x66756e2d657468657265756d2d7365706f6c69612d3100000000000000000000"
    },
    avalancheFuji: {
      router: "0xA9d587a00A31A52Ed70D6026794a8FC5E2F5dCb0",
      donId: "0x66756e2d6176616c616e6368652d66756a692d31000000000000000000000000"
    }
  };

  const networkConfig = config[network.name];
  if (!networkConfig) {
    throw new Error(`Network ${network.name} not supported`);
  }

  await deploy("AIMarketplace", {
    from: deployer,
    args: [
      networkConfig.router,
      networkConfig.donId,
      process.env.SUBSCRIPTION_ID,
      aiAnalysisSource
    ],
    log: true,
  });
};
```

## Testing Integration

### 1. Local Testing

Test the integration locally using the mock AI service:

```bash
# Start the AI service locally
cd packages/ai-service
./deploy.sh local

# In another terminal, test the integration
curl -X POST http://localhost:5001/analyze/single \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 1,
    "title": "Test Product",
    "description": "This is a test product for integration",
    "seller": "0x123...",
    "tags": ["test"],
    "timestamp": 1717872000
  }'
```

### 2. Functions Simulation

Use Chainlink Functions simulation to test the integration:

```bash
# Install Chainlink Functions toolkit
npm install @chainlink/functions-toolkit

# Create simulation script
node simulate-ai-analysis.js
```

### 3. End-to-End Testing

Deploy to testnet and test the full integration:

```bash
# Deploy contracts
npx hardhat deploy --network sepolia

# Add a product (triggers AI analysis)
npx hardhat run scripts/add-test-product.js --network sepolia

# Check analysis results
npx hardhat run scripts/check-analysis.js --network sepolia
```

## Production Considerations

### 1. Error Handling

- Implement retry logic for failed AI service requests
- Handle network timeouts and service unavailability
- Provide fallback scoring mechanisms

### 2. Cost Optimization

- Batch multiple products in single requests when possible
- Cache analysis results to avoid duplicate requests
- Use appropriate gas limits for Functions calls

### 3. Security

- Validate all input data before sending to AI service
- Implement access controls for analysis requests
- Monitor for unusual patterns or potential attacks

### 4. Monitoring

- Track AI service response times and accuracy
- Monitor Functions request success rates
- Set up alerts for service failures

## Example Use Cases

### 1. Marketplace Quality Control

```solidity
modifier onlyQualityProducts(uint256 productId) {
    require(isProductTrusted(productId), "Product quality too low");
    _;
}

function purchaseProduct(uint256 productId) external payable onlyQualityProducts(productId) {
    // Purchase logic
}
```

### 2. Dynamic Pricing

```solidity
function getProductPrice(uint256 productId) external view returns (uint256) {
    uint8 score = products[productId].aiScore;
    uint256 basePrice = products[productId].basePrice;
    
    // Higher quality products can command premium pricing
    if (score >= 90) return basePrice * 120 / 100; // 20% premium
    if (score >= 80) return basePrice * 110 / 100; // 10% premium
    if (score >= 70) return basePrice;
    
    return basePrice * 90 / 100; // 10% discount for lower quality
}
```

### 3. Reputation System

```solidity
function updateSellerReputation(address seller) external {
    uint256 totalScore = 0;
    uint256 productCount = 0;
    
    // Calculate average AI score for seller's products
    for (uint256 i = 0; i < sellerProducts[seller].length; i++) {
        uint256 productId = sellerProducts[seller][i];
        if (products[productId].analyzed) {
            totalScore += products[productId].aiScore;
            productCount++;
        }
    }
    
    if (productCount > 0) {
        sellerReputation[seller] = totalScore / productCount;
    }
}
```

## Support and Troubleshooting

For issues with the AI service integration:

1. Check the AI service health endpoint
2. Verify Chainlink Functions subscription has sufficient LINK
3. Review Functions request logs in the Chainlink dashboard
4. Test with debug mode enabled for detailed analysis information

For additional support, refer to:
- [Chainlink Functions Documentation](https://docs.chain.link/chainlink-functions)
- [AI Service API Documentation](./API.md)
- [Deployment Guide](./DEPLOYMENT.md)
