{"name": "@se-2/hardhat", "version": "0.0.1", "scripts": {"account": "hardhat run scripts/listAccount.ts", "account:generate": "hardhat run scripts/generateAccount.ts", "account:import": "hardhat run scripts/importAccount.ts", "account:reveal-pk": "hardhat run scripts/revealPK.ts", "chain": "hardhat node --network hardhat --no-deploy", "check-types": "tsc --noEmit --incremental", "clean": "hardhat clean", "compile": "hardhat compile", "deploy": "ts-node scripts/runHardhatDeployWithPK.ts", "flatten": "hardhat flatten", "fork": "MAINNET_FORKING_ENABLED=true hardhat node --network hardhat --no-deploy", "format": "prettier --write './**/*.(ts|sol)'", "generate": "yarn account:generate", "hardhat-verify": "hardhat verify", "lint": "eslint", "lint-staged": "eslint", "test": "REPORT_GAS=true hardhat test --network hardhat", "verify": "hardhat etherscan-verify"}, "dependencies": {"@inquirer/password": "^4.0.2", "@openzeppelin/contracts": "~5.0.2", "@typechain/ethers-v6": "~0.5.1", "dotenv": "~16.4.5", "envfile": "~7.1.0", "qrcode": "~1.5.4"}, "devDependencies": {"@ethersproject/abi": "~5.7.0", "@ethersproject/providers": "~5.7.2", "@nomicfoundation/hardhat-chai-matchers": "~2.0.7", "@nomicfoundation/hardhat-ethers": "~3.0.8", "@nomicfoundation/hardhat-network-helpers": "~1.0.11", "@nomicfoundation/hardhat-verify": "~2.0.10", "@typechain/ethers-v5": "~11.1.2", "@typechain/hardhat": "~9.1.0", "@types/eslint": "~9.6.1", "@types/mocha": "~10.0.10", "@types/prettier": "~3.0.0", "@types/qrcode": "~1.5.5", "@typescript-eslint/eslint-plugin": "~8.27.0", "@typescript-eslint/parser": "~8.27.0", "chai": "~4.5.0", "eslint": "~9.23.0", "eslint-config-prettier": "~10.1.1", "eslint-plugin-prettier": "~5.2.4", "ethers": "~6.13.2", "hardhat": "~2.22.10", "hardhat-deploy": "~0.12.4", "hardhat-deploy-ethers": "~0.4.2", "hardhat-gas-reporter": "~2.2.1", "prettier": "^3.5.3", "prettier-plugin-solidity": "~1.4.1", "solidity-coverage": "~0.8.13", "ts-node": "~10.9.1", "typechain": "~8.3.2", "typescript": "^5.8.2"}}