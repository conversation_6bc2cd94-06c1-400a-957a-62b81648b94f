{"name": "@se-2/nextjs", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "check-types": "tsc --noEmit --incremental", "dev": "next dev", "format": "prettier --write . '!(node_modules|.next|contracts)/**/*'", "ipfs": "NEXT_PUBLIC_IPFS_BUILD=true yarn build && yarn bgipfs upload config init -u https://upload.bgipfs.com && CID=$(yarn bgipfs upload out | grep -o 'CID: [^ ]*' | cut -d' ' -f2) && [ ! -z \"$CID\" ] && echo '🚀 Upload complete! Your site is now available at: https://community.bgipfs.com/ipfs/'$CID || echo '❌ Upload failed'", "lint": "next lint", "serve": "next start", "start": "next dev", "vercel": "vercel --build-env YARN_ENABLE_IMMUTABLE_INSTALLS=false --build-env ENABLE_EXPERIMENTAL_COREPACK=1 --build-env VERCEL_TELEMETRY_DISABLED=1", "vercel:login": "vercel login", "vercel:yolo": "vercel --build-env YARN_ENABLE_IMMUTABLE_INSTALLS=false --build-env ENABLE_EXPERIMENTAL_COREPACK=1 --build-env NEXT_PUBLIC_IGNORE_BUILD_ERROR=true --build-env VERCEL_TELEMETRY_DISABLED=1"}, "dependencies": {"@heroicons/react": "~2.1.5", "@rainbow-me/rainbowkit": "2.2.5", "@tanstack/react-query": "~5.59.15", "@uniswap/sdk-core": "~5.8.2", "@uniswap/v2-sdk": "~4.6.1", "blo": "~1.2.0", "burner-connector": "0.0.14", "daisyui": "5.0.9", "kubo-rpc-client": "~5.0.2", "next": "~15.2.3", "next-nprogress-bar": "~2.3.13", "next-themes": "~0.3.0", "qrcode.react": "~4.0.1", "react": "~19.0.0", "react-dom": "~19.0.0", "react-hot-toast": "~2.4.0", "usehooks-ts": "~3.1.0", "viem": "2.30.0", "wagmi": "2.15.4", "zustand": "~5.0.0"}, "devDependencies": {"@tailwindcss/postcss": "4.0.15", "@trivago/prettier-plugin-sort-imports": "~4.3.0", "@types/node": "~18.19.50", "@types/react": "~19.0.7", "abitype": "1.0.6", "autoprefixer": "~10.4.20", "bgipfs": "~0.0.12", "eslint": "~9.23.0", "eslint-config-next": "~15.2.3", "eslint-config-prettier": "~10.1.1", "eslint-plugin-prettier": "~5.2.4", "postcss": "~8.4.45", "prettier": "~3.5.3", "tailwindcss": "4.1.3", "type-fest": "~4.26.1", "typescript": "~5.8.2", "vercel": "~39.1.3"}, "packageManager": "yarn@3.2.3"}